# Windmill 异步调用迁移说明

## 问题背景

在运行金融分析系统时，遇到了以下错误：

```
2025-08-01 03:55:26.517 | DEBUG    | financial_analysis.utils:safe_json_parse:187 - Windmill新闻搜索API响应状态码: 201
2025-08-01 03:55:26.517 | DEBUG    | financial_analysis.utils:safe_json_parse:189 - Windmill新闻搜索API响应内容: 019863c5-39b1-bebe-5cfc-f33972ead0db...
2025-08-01 03:55:26.517 | ERROR    | financial_analysis.utils:safe_json_parse:200 - Windmill新闻搜索APIJSON解析失败: Extra data: line 1 column 2 (char 1)
2025-08-01 03:55:26.517 | ERROR    | financial_analysis.utils:safe_json_parse:201 - 响应内容: 019863c5-39b1-bebe-5cfc-f33972ead0db
```

## 问题分析

错误的根本原因是 **Windmill API 使用异步执行模式**：

1. **同步调用问题**: 原代码使用同步 HTTP 请求直接调用 Windmill API
2. **响应格式误解**: Windmill API 返回的是任务 UUID，而不是直接的 JSON 结果
3. **缺少轮询机制**: 没有实现任务状态轮询和结果获取

## Windmill 异步执行流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Windmill as Windmill API
    participant Worker as 工作节点

    Client->>Windmill: POST /jobs/run (触发作业)
    Windmill-->>Client: 返回任务UUID (201)
    
    Windmill->>Worker: 分发任务
    Worker->>Worker: 执行任务
    
    loop 轮询状态
        Client->>Windmill: GET /jobs_u/completed/get_result_maybe/{uuid}
        alt 任务未完成
            Windmill-->>Client: {"completed": false}
        else 任务完成
            Windmill-->>Client: {"completed": true, "result": {...}}
        end
    end
```

## 解决方案

### 1. 创建异步 Windmill 客户端

创建了 `financial_analysis/windmill_client.py`，提供完整的异步调用功能：

- **异步作业触发**: `trigger_job()` 方法
- **状态轮询**: `wait_for_job_completion()` 方法  
- **完整执行流程**: `execute_job()` 方法
- **文本分析专用**: `generate_text_analysis()` 方法

### 2. 修改现有模块

#### 2.1 新闻搜索模块 (`news_search.py`)

**修改前**:
```python
def _search_via_windmill(self, search_query: str, stock_info: StockInfo) -> List[NewsItem]:
    # 直接同步调用 Windmill API
    response = requests.post(url, headers=headers, json=payload, timeout=30)
    result = safe_json_parse(response, "Windmill新闻搜索API")  # 这里会失败
```

**修改后**:
```python
def _search_via_windmill(self, search_query: str, stock_info: StockInfo) -> List[NewsItem]:
    # 使用异步客户端执行搜索
    return asyncio.run(self._async_search_via_windmill(search_query, stock_info))

async def _async_search_via_windmill(self, search_query: str, stock_info: StockInfo) -> List[NewsItem]:
    # 使用异步客户端执行作业
    result = await windmill_client.execute_job(
        folder=settings.windmill_folder,
        script=settings.windmill_script,
        payload=payload,
        max_wait_time=120
    )
```

#### 2.2 热点新闻分析模块 (`hot_news_analyzer.py`)

**修改前**:
```python
def _analyze_via_text_generation(self, news_item: HotNewsItem) -> Optional[Dict[str, Any]]:
    # 直接同步调用 Windmill API
    response = requests.post(url, headers=headers, json=payload, timeout=30)
    result = safe_json_parse(response, "Windmill新闻分析API")  # 这里会失败
```

**修改后**:
```python
def _analyze_via_text_generation(self, news_item: HotNewsItem) -> Optional[Dict[str, Any]]:
    # 使用异步客户端进行分析
    return asyncio.run(self._async_analyze_via_text_generation(news_item))

async def _async_analyze_via_text_generation(self, news_item: HotNewsItem) -> Optional[Dict[str, Any]]:
    # 使用异步客户端执行作业
    result = await windmill_client.execute_job(
        folder=settings.windmill_folder,
        script=settings.windmill_script,
        payload=payload,
        max_wait_time=90
    )
```

#### 2.3 分析引擎模块 (`analysis.py`)

分析引擎模块已经实现了异步调用，使用了正确的异步模式：

```python
async def _call_windmill_analysis_async(self, analysis_data: Dict[str, Any]) -> Optional[str]:
    # 使用异步客户端生成文本分析
    analysis = await windmill_client.generate_text_analysis(
        prompt=prompt,
        system_instruction=system_instruction,
        response_schema=response_schema
    )
    return analysis
```

### 3. 关键改进点

#### 3.1 异步执行流程
- **触发阶段**: 发送作业请求，获取任务 UUID
- **等待阶段**: 轮询任务状态直到完成或超时
- **结果获取**: 从完成的任务中提取结果数据

#### 3.2 错误处理和重试
- **超时处理**: 设置合理的最大等待时间
- **轮询间隔**: 避免过于频繁的状态查询
- **异常恢复**: 在异步调用失败时降级到简单分析

#### 3.3 性能优化
- **并发支持**: 支持多个任务并发执行
- **连接复用**: 使用 aiohttp 的连接池
- **资源管理**: 正确管理异步资源的生命周期

## 测试验证

创建了 `test_windmill_async.py` 测试脚本，包含以下测试用例：

1. **基本文本生成测试**: 验证简单的文本生成功能
2. **作业执行测试**: 验证完整的作业执行流程
3. **触发和等待分离测试**: 验证任务触发和状态轮询的分离
4. **并发作业处理测试**: 验证多任务并发执行能力

运行测试：
```bash
python test_windmill_async.py
```

## 配置要求

确保 `.env` 文件中包含正确的 Windmill 配置：

```env
WINDMILL_BASE_URL=https://wm.atjog.com
WINDMILL_TOKEN=your_access_token
WINDMILL_WORKSPACE=my-workspace
WINDMILL_FOLDER=gemini
WINDMILL_SCRIPT=text_generation
```

## 依赖要求

需要安装 aiohttp 依赖：

```bash
pip install aiohttp>=3.8.0
```

## 使用示例

### 基本使用
```python
import asyncio
from financial_analysis.windmill_client import windmill_client

async def example():
    result = await windmill_client.execute_job(
        folder="gemini",
        script="text_generation",
        payload={"prompt": "分析苹果公司股票"}
    )
    print(result)

asyncio.run(example())
```

### 文本分析
```python
async def analysis_example():
    analysis = await windmill_client.generate_text_analysis(
        prompt="请分析苹果公司的投资价值",
        system_instruction="你是专业的金融分析师"
    )
    print(analysis)
```

## 注意事项

1. **异步上下文**: 所有 Windmill 调用现在都需要在异步上下文中执行
2. **等待时间**: 根据任务复杂度设置合理的 `max_wait_time`
3. **错误处理**: 异步调用失败时会自动降级到简单分析
4. **资源管理**: 异步客户端会自动管理连接池和资源清理

## 迁移完成

通过这次迁移，系统现在能够：

- ✅ 正确处理 Windmill 的异步执行模式
- ✅ 避免 JSON 解析错误
- ✅ 支持任务状态轮询和结果获取
- ✅ 提供更好的错误处理和降级机制
- ✅ 支持并发任务执行
- ✅ 保持与现有代码的兼容性

这解决了原始错误中的 "Extra data: line 1 column 2 (char 1)" JSON 解析问题，因为现在我们正确地处理了 Windmill 返回的任务 UUID，并通过轮询获取实际的执行结果。
