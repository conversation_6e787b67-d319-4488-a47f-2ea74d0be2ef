"""
热点信息分析器模块

使用Gemini大语言模型对热点信息进行分析，包括：
- 判断是否为历史信息
- 情感分析
- 重要程度评估
- 关键词提取
"""

import json
import time
import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import requests
from loguru import logger

from .models import HotNewsItem
from .config import settings
from .utils import safe_json_parse
from .windmill_client import windmill_client


class HotNewsAnalyzer:
    """热点信息分析器类"""
    
    def __init__(self):
        """初始化热点信息分析器"""
        self._cache = {}  # 分析结果缓存
        self._cache_timeout = 3600  # 缓存1小时
        logger.info("热点信息分析器初始化完成")
    
    def analyze_news_batch(self, news_items: List[HotNewsItem]) -> List[HotNewsItem]:
        """
        批量分析热点信息
        
        Args:
            news_items: 待分析的新闻列表
            
        Returns:
            分析后的新闻列表
        """
        try:
            analyzed_news = []
            
            for news in news_items:
                analyzed_news_item = self.analyze_single_news(news)
                analyzed_news.append(analyzed_news_item)
                
                # 避免API调用过于频繁
                time.sleep(0.1)
            
            logger.info(f"完成 {len(analyzed_news)} 条新闻的分析")
            return analyzed_news
            
        except Exception as e:
            logger.error(f"批量分析新闻失败: {str(e)}")
            return news_items
    
    def analyze_single_news(self, news_item: HotNewsItem) -> HotNewsItem:
        """
        分析单条热点信息
        
        Args:
            news_item: 待分析的新闻
            
        Returns:
            分析后的新闻
        """
        try:
            # 检查缓存
            cache_key = f"analysis_{news_item.news_id}"
            if self._is_cache_valid(cache_key):
                cached_result = self._cache[cache_key]['data']
                return self._apply_analysis_result(news_item, cached_result)
            
            # 调用生成文本接口进行分析
            analysis_result = self._analyze_via_text_generation(news_item)
            
            # 缓存结果
            if analysis_result:
                self._cache[cache_key] = {
                    'data': analysis_result,
                    'timestamp': time.time()
                }
            
            # 应用分析结果
            return self._apply_analysis_result(news_item, analysis_result)
            
        except Exception as e:
            logger.error(f"分析新闻失败 {news_item.news_id}: {str(e)}")
            return news_item
    
    def _analyze_via_text_generation(self, news_item: HotNewsItem) -> Optional[Dict[str, Any]]:
        """通过Windmill生成文本接口分析新闻"""
        try:
            if not settings.windmill_base_url or not settings.windmill_token:
                logger.warning("Windmill配置不完整，使用简单分析")
                return self._simple_analysis(news_item)

            # 使用异步客户端进行分析
            return asyncio.run(self._async_analyze_via_text_generation(news_item))

        except Exception as e:
            logger.error(f"生成文本接口分析失败: {str(e)}")
            return self._simple_analysis(news_item)

    async def _async_analyze_via_text_generation(self, news_item: HotNewsItem) -> Optional[Dict[str, Any]]:
        """异步通过Windmill生成文本接口分析新闻"""
        try:
            # 构造分析提示词
            prompt = self._build_analysis_prompt(news_item)

            # 定义响应结构
            response_schema = {
                "type": "object",
                "properties": {
                    "is_historical": {
                        "type": "boolean",
                        "description": "是否为历史信息"
                    },
                    "sentiment": {
                        "type": "string",
                        "enum": ["positive", "negative", "neutral"],
                        "description": "情感倾向"
                    },
                    "importance_level": {
                        "type": "string",
                        "enum": ["high", "medium", "low"],
                        "description": "重要程度"
                    },
                    "keywords": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "关键词列表"
                    },
                    "category": {
                        "type": "string",
                        "description": "新闻分类"
                    },
                    "summary": {
                        "type": "string",
                        "description": "新闻摘要"
                    },
                    "heat_score": {
                        "type": "number",
                        "description": "热度评分(0-100)"
                    }
                },
                "required": ["is_historical", "sentiment", "importance_level", "keywords"]
            }

            # 构建请求参数
            payload = {
                "prompt": prompt,
                "system_instruction": "你是一个专业的新闻分析师，请根据提供的新闻内容进行分析。",
                "responseSchema": response_schema
            }

            logger.debug(f"开始异步分析新闻: {news_item.news_id}")

            # 使用异步客户端执行作业
            result = await windmill_client.execute_job(
                folder=settings.windmill_folder,
                script=settings.windmill_script,
                payload=payload,
                max_wait_time=90  # 新闻分析通常较快
            )

            if not result:
                logger.warning("Windmill新闻分析未返回结果")
                return self._simple_analysis(news_item)

            # 提取分析结果
            if 'result' in result:
                analysis_result = result['result']
                if isinstance(analysis_result, dict):
                    logger.info(f"新闻分析完成: {news_item.news_id}")
                    return analysis_result
                else:
                    logger.warning("Windmill返回的分析结果格式异常")
                    return self._simple_analysis(news_item)
            else:
                logger.warning("Windmill结果中缺少result字段")
                return self._simple_analysis(news_item)

        except Exception as e:
            logger.error(f"异步新闻分析失败: {str(e)}")
            return self._simple_analysis(news_item)

    def check_historical_news_with_search(self, news_items: List[HotNewsItem]) -> List[HotNewsItem]:
        """
        使用搜索工具批量检查新闻是否为历史消息

        Args:
            news_items: 待检查的新闻列表

        Returns:
            更新了历史信息标记的新闻列表
        """
        try:
            if not settings.windmill_base_url or not settings.windmill_token:
                logger.warning("Windmill配置不完整，无法使用搜索功能")
                return news_items

            # 使用异步客户端进行搜索
            return asyncio.run(self._async_check_historical_news_with_search(news_items))

        except Exception as e:
            logger.error(f"使用搜索工具检查历史消息失败: {str(e)}")
            return news_items

    async def _async_check_historical_news_with_search(self, news_items: List[HotNewsItem]) -> List[HotNewsItem]:
        """异步使用搜索工具批量检查新闻是否为历史消息"""
        try:
            # 构造搜索提示词
            search_prompt = self._build_search_prompt(news_items)

            # 构建请求参数
            payload = {
                "prompt": search_prompt,
                "search": True
            }

            logger.info("开始异步使用搜索工具检查历史消息")

            # 使用异步客户端执行作业
            result = await windmill_client.execute_job(
                folder="gemini",  # 使用固定的gemini文件夹
                script="text_generation",  # 使用text_generation脚本
                payload=payload,
                max_wait_time=120  # 搜索可能需要更长时间
            )

            if not result:
                logger.warning("Windmill历史消息检查未返回结果")
                return news_items

            # 提取搜索结果
            if 'result' in result:
                # 解析搜索结果并更新新闻项
                updated_news = self._parse_search_result(news_items, result['result'])
                logger.info(f"完成 {len(updated_news)} 条新闻的历史信息检查")
                return updated_news
            else:
                logger.warning("搜索结果格式异常，使用原始新闻列表")
                return news_items

        except Exception as e:
            logger.error(f"异步历史消息检查失败: {str(e)}")
            return news_items
    
    def _build_analysis_prompt(self, news_item: HotNewsItem) -> str:
        """构造分析提示词"""
        current_date = datetime.now().strftime("%Y-%m-%d")
        check_days = settings.hot_news_history_check_days
        
        prompt = f"""
请分析以下新闻信息：

标题：{news_item.title}
内容：{news_item.content or '无详细内容'}
来源：{news_item.source}
发布时间：{news_item.publish_time.strftime("%Y-%m-%d %H:%M:%S")}
获取时间：{news_item.fetch_time.strftime("%Y-%m-%d %H:%M:%S")}

当前日期：{current_date}

请进行以下分析：

1. 历史信息判断：
   - 判断这条新闻是否是{check_days}天前就已经发布过的历史信息
   - 考虑新闻的时效性和是否为重复内容
   - 如果是突发事件的后续报道，不算历史信息

2. 情感分析：
   - positive：正面情感，积极向上的内容
   - negative：负面情感，消极或负面的内容
   - neutral：中性情感，客观报道

3. 重要程度评估：
   - high：重大新闻，影响面广，具有重要意义
   - medium：一般重要新闻，有一定影响力
   - low：普通新闻，影响力较小

4. 关键词提取：提取3-5个最重要的关键词

5. 新闻分类：如财经、科技、政治、社会等

6. 新闻摘要：用1-2句话概括新闻要点

7. 热度评分：根据新闻的重要性、时效性、影响力等给出0-100的评分
"""
        
        return prompt

    def _build_search_prompt(self, news_items: List[HotNewsItem]) -> str:
        """
        构造用于搜索历史消息的提示词

        Args:
            news_items: 新闻列表

        Returns:
            搜索提示词
        """
        current_date = datetime.now().strftime("%Y-%m-%d")
        check_days = settings.hot_news_history_check_days

        # 构造新闻列表
        news_list = []
        for i, news in enumerate(news_items, 1):
            news_info = f"""
{i}. 标题: {news.title}
   内容: {news.content[:200] if news.content else '无详细内容'}...
   来源: {news.source}
   声明的发布时间: {news.publish_time.strftime("%Y-%m-%d %H:%M:%S")}
   获取时间: {news.fetch_time.strftime("%Y-%m-%d %H:%M:%S")}
"""
            news_list.append(news_info)

        prompt = f"""
请帮我检查以下新闻列表中的每条新闻是否为历史消息。我需要你使用搜索工具来查找这些新闻的最初发布时间，以判断它们是否是{check_days}天前就已经发布过的历史信息。

当前日期: {current_date}
历史消息判断标准: 如果新闻的最初发布时间距离当前日期超过{check_days}天，则认为是历史消息。

新闻列表:
{''.join(news_list)}

请使用搜索工具查找每条新闻的相关信息，并按以下格式返回结果：

格式要求:
1. 新闻编号: [编号]
2. 是否为历史消息: [是/否]
3. 最初发布时间: [YYYY-MM-DD HH:MM:SS 或 "未找到"]
4. 判断依据: [简要说明搜索到的相关信息]

请确保对每条新闻都进行搜索和判断，如果搜索不到相关信息，请标注"未找到"并说明原因。

注意事项:
- 重点关注新闻的最初发布时间，而不是转载时间
- 如果是突发事件的后续报道或更新，不算历史消息
- 如果搜索结果显示该新闻确实是近期首次发布，则不是历史消息
"""

        return prompt

    def _parse_search_result(self, news_items: List[HotNewsItem], search_result: str) -> List[HotNewsItem]:
        """
        解析搜索结果并更新新闻项的历史信息标记

        Args:
            news_items: 原始新闻列表
            search_result: 搜索结果文本

        Returns:
            更新后的新闻列表
        """
        try:
            # 创建结果字典，用于存储每个新闻的解析结果
            news_results = {}
            current_date = datetime.now()
            check_days = settings.hot_news_history_check_days

            # 使用更简单的解析方法：按空行分割新闻项
            news_blocks = search_result.strip().split('\n\n')

            for block in news_blocks:
                if not block.strip():
                    continue

                lines = block.strip().split('\n')
                news_index = -1
                is_historical = False
                original_publish_time = None

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    # 提取新闻编号
                    if '新闻编号:' in line:
                        try:
                            news_index = int(line.split('新闻编号:')[1].strip()) - 1
                        except:
                            pass
                    elif line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')):
                        # 检查是否是新闻编号行
                        if '新闻编号' in line:
                            try:
                                news_index = int(line.split('.')[0]) - 1
                            except:
                                pass

                    # 解析是否为历史消息
                    if '是否为历史消息:' in line:
                        result_text = line.split(':')[1].strip().lower()
                        is_historical = '是' in result_text or 'true' in result_text or '历史' in result_text

                    # 解析最初发布时间
                    elif '最初发布时间:' in line:
                        time_text = line.split(':')[1].strip()
                        if time_text != "未找到" and len(time_text) > 10:
                            original_publish_time = time_text

                            # 基于时间判断是否为历史消息
                            try:
                                parsed_time = datetime.strptime(time_text[:19], "%Y-%m-%d %H:%M:%S")
                                days_diff = (current_date - parsed_time).days
                                if days_diff > check_days:
                                    is_historical = True
                            except:
                                pass

                # 保存解析结果
                if news_index >= 0:
                    news_results[news_index] = {
                        'is_historical': is_historical,
                        'original_publish_time': original_publish_time
                    }

            # 创建更新后的新闻列表
            updated_news = []
            for i, news in enumerate(news_items):
                news_copy = news.model_copy()  # 使用 model_copy 替代 copy

                if i in news_results:
                    result = news_results[i]
                    news_copy.is_historical = result['is_historical']

                    # 更新发布时间（如果找到了原始时间）
                    if result['original_publish_time']:
                        try:
                            parsed_time = datetime.strptime(result['original_publish_time'][:19], "%Y-%m-%d %H:%M:%S")
                            news_copy.publish_time = parsed_time
                        except:
                            pass
                else:
                    # 如果没有解析到结果，默认为非历史消息
                    news_copy.is_historical = False

                updated_news.append(news_copy)

            # 统计结果
            historical_count = sum(1 for news in updated_news if news.is_historical)
            logger.info(f"搜索结果: {len(updated_news)} 条新闻中有 {historical_count} 条被标记为历史消息")

            return updated_news

        except Exception as e:
            logger.error(f"解析搜索结果失败: {str(e)}")
            # 返回原始新闻列表的副本，标记为非历史消息
            updated_news = []
            for news in news_items:
                news_copy = news.model_copy()
                news_copy.is_historical = False
                updated_news.append(news_copy)
            return updated_news
    
    def _simple_analysis(self, news_item: HotNewsItem) -> Dict[str, Any]:
        """简单分析（当Gemini不可用时）"""
        try:
            # 基于发布时间判断是否为历史信息
            days_ago = (datetime.now() - news_item.publish_time).days
            is_historical = days_ago > settings.hot_news_history_check_days
            
            # 简单的关键词提取
            title_words = news_item.title.split()
            keywords = [word for word in title_words if len(word) > 1][:5]
            
            # 默认分析结果
            return {
                "is_historical": is_historical,
                "sentiment": "neutral",
                "importance_level": "medium",
                "keywords": keywords,
                "category": "综合",
                "summary": news_item.title[:100],
                "heat_score": 50.0
            }
            
        except Exception as e:
            logger.error(f"简单分析失败: {str(e)}")
            return {
                "is_historical": False,
                "sentiment": "neutral",
                "importance_level": "medium",
                "keywords": [],
                "category": "综合",
                "summary": news_item.title,
                "heat_score": 50.0
            }
    
    def _apply_analysis_result(self, news_item: HotNewsItem, analysis_result: Optional[Dict[str, Any]]) -> HotNewsItem:
        """应用分析结果到新闻条目"""
        if not analysis_result:
            return news_item
        
        try:
            # 更新新闻条目的分析字段
            news_item.is_historical = analysis_result.get("is_historical", False)
            news_item.sentiment = analysis_result.get("sentiment", "neutral")
            news_item.importance_level = analysis_result.get("importance_level", "medium")
            news_item.keywords = analysis_result.get("keywords", [])
            news_item.category = analysis_result.get("category")
            news_item.summary = analysis_result.get("summary")
            news_item.heat_score = analysis_result.get("heat_score")
            
            return news_item
            
        except Exception as e:
            logger.error(f"应用分析结果失败: {str(e)}")
            return news_item
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache:
            return False
        
        cache_time = self._cache[cache_key]['timestamp']
        return time.time() - cache_time < self._cache_timeout
    
    def filter_historical_news(self, news_items: List[HotNewsItem]) -> List[HotNewsItem]:
        """过滤历史信息"""
        try:
            filtered_news = [news for news in news_items if not news.is_historical]
            
            logger.info(f"过滤前: {len(news_items)} 条，过滤后: {len(filtered_news)} 条")
            return filtered_news
            
        except Exception as e:
            logger.error(f"过滤历史信息失败: {str(e)}")
            return news_items
    
    def filter_by_importance(self, news_items: List[HotNewsItem], min_importance: str = "medium") -> List[HotNewsItem]:
        """根据重要程度过滤新闻"""
        try:
            importance_levels = {"low": 1, "medium": 2, "high": 3}
            min_level = importance_levels.get(min_importance, 2)
            
            filtered_news = []
            for news in news_items:
                news_level = importance_levels.get(news.importance_level, 2)
                if news_level >= min_level:
                    filtered_news.append(news)
            
            logger.info(f"按重要程度过滤前: {len(news_items)} 条，过滤后: {len(filtered_news)} 条")
            return filtered_news
            
        except Exception as e:
            logger.error(f"按重要程度过滤失败: {str(e)}")
            return news_items
