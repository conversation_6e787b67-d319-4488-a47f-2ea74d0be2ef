"""
Windmill 异步客户端模块

提供对 Windmill 工作流平台的异步调用功能，支持作业触发、状态轮询和结果获取。
基于 Windmill API 实现异步任务执行和结果等待机制。
"""

import asyncio
import json
import time
from typing import Optional, Dict, Any, Union
import aiohttp
from loguru import logger

from .config import settings
from .utils import format_error_message


class WindmillClient:
    """Windmill 异步客户端类"""
    
    def __init__(self, base_url: Optional[str] = None, token: Optional[str] = None, 
                 workspace: Optional[str] = None):
        """
        初始化 Windmill 客户端
        
        Args:
            base_url: Windmill 服务基础URL，默认从配置读取
            token: 访问令牌，默认从配置读取
            workspace: 工作空间名称，默认从配置读取
        """
        self.base_url = base_url or settings.windmill_base_url
        self.token = token or settings.windmill_token
        self.workspace = workspace or settings.windmill_workspace
        
        if not self.base_url or not self.token:
            logger.warning("Windmill 配置不完整，某些功能可能无法使用")
        
        # 默认请求头
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}" if self.token else ""
        }
        
        logger.info(f"Windmill 客户端初始化完成: {self.base_url}")
    
    async def trigger_job(self, folder: str, script: str, payload: Dict[str, Any] = None) -> Optional[str]:
        """
        触发 Windmill 作业
        
        Args:
            folder: 文件夹路径
            script: 脚本名称
            payload: 作业参数，默认为空字典
            
        Returns:
            作业UUID，如果触发失败返回None
        """
        if not self.base_url or not self.token:
            logger.error("Windmill 配置不完整，无法触发作业")
            return None
        
        if payload is None:
            payload = {}
        
        try:
            # 构建API端点URL
            endpoint = f"{self.base_url}/api/w/{self.workspace}/jobs/run/p/f/{folder}/{script}"
            
            logger.debug(f"触发 Windmill 作业: {endpoint}")
            logger.debug(f"作业参数: {json.dumps(payload, ensure_ascii=False)}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    endpoint,
                    headers=self.headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    if response.status not in [200, 201]:
                        logger.error(f"触发作业失败，状态码: {response.status}")
                        error_text = await response.text()
                        logger.error(f"错误响应: {error_text}")
                        return None
                    
                    # 获取作业UUID
                    job_uuid = await response.text()
                    job_uuid = job_uuid.strip().strip('"')  # 移除可能的引号
                    
                    logger.info(f"作业触发成功，UUID: {job_uuid}")
                    return job_uuid
                    
        except asyncio.TimeoutError:
            logger.error("触发作业超时")
            return None
        except Exception as e:
            logger.error(f"触发作业失败: {format_error_message(e, '触发Windmill作业')}")
            return None
    
    async def wait_for_job_completion(self, job_uuid: str, 
                                    max_wait_time: int = 300, 
                                    poll_interval: int = 1) -> Optional[Dict[str, Any]]:
        """
        等待作业完成并获取结果
        
        Args:
            job_uuid: 作业UUID
            max_wait_time: 最大等待时间（秒），默认5分钟
            poll_interval: 轮询间隔（秒），默认1秒
            
        Returns:
            作业完成结果，如果失败或超时返回None
        """
        if not self.base_url or not self.token:
            logger.error("Windmill 配置不完整，无法等待作业完成")
            return None
        
        try:
            # 构建结果查询端点URL
            endpoint = f"{self.base_url}/api/w/{self.workspace}/jobs_u/completed/get_result_maybe/{job_uuid}"
            
            logger.debug(f"等待作业完成: {job_uuid}")
            
            start_time = time.time()
            
            async with aiohttp.ClientSession() as session:
                while time.time() - start_time < max_wait_time:
                    try:
                        async with session.get(
                            endpoint,
                            headers=self.headers,
                            timeout=aiohttp.ClientTimeout(total=10)
                        ) as response:
                            
                            if response.status != 200:
                                logger.warning(f"查询作业状态失败，状态码: {response.status}")
                                await asyncio.sleep(poll_interval)
                                continue
                            
                            # 解析响应
                            result_data = await response.json()
                            
                            # 检查作业是否完成
                            if result_data.get('completed', False):
                                logger.info(f"作业完成: {job_uuid}")
                                return result_data
                            else:
                                # 作业未完成，继续等待
                                elapsed_time = time.time() - start_time
                                logger.debug(f"作业进行中，已等待 {elapsed_time:.1f} 秒")
                                await asyncio.sleep(poll_interval)
                                
                    except asyncio.TimeoutError:
                        logger.warning("查询作业状态超时，继续重试")
                        await asyncio.sleep(poll_interval)
                        continue
                    except Exception as e:
                        logger.warning(f"查询作业状态异常: {str(e)}，继续重试")
                        await asyncio.sleep(poll_interval)
                        continue
                
                # 超时
                logger.error(f"等待作业完成超时: {job_uuid} (超时时间: {max_wait_time}秒)")
                return None
                
        except Exception as e:
            logger.error(f"等待作业完成失败: {format_error_message(e, '等待Windmill作业完成')}")
            return None
    
    async def execute_job(self, folder: str, script: str, payload: Dict[str, Any] = None,
                         max_wait_time: int = 300, poll_interval: int = 1) -> Optional[Dict[str, Any]]:
        """
        执行完整的作业流程：触发作业 -> 等待完成 -> 获取结果
        
        Args:
            folder: 文件夹路径
            script: 脚本名称
            payload: 作业参数
            max_wait_time: 最大等待时间（秒）
            poll_interval: 轮询间隔（秒）
            
        Returns:
            作业执行结果，如果失败返回None
        """
        try:
            logger.info(f"开始执行 Windmill 作业: {folder}/{script}")
            
            # 1. 触发作业
            job_uuid = await self.trigger_job(folder, script, payload)
            if not job_uuid:
                logger.error("作业触发失败")
                return None
            
            # 2. 等待作业完成
            result = await self.wait_for_job_completion(job_uuid, max_wait_time, poll_interval)
            if not result:
                logger.error(f"作业执行失败或超时: {job_uuid}")
                return None
            
            logger.info(f"作业执行成功: {job_uuid}")
            return result
            
        except Exception as e:
            logger.error(f"执行作业失败: {format_error_message(e, '执行Windmill作业')}")
            return None
    
    async def generate_text_analysis(self, prompt: str, system_instruction: str = None,
                                   response_schema: Dict[str, Any] = None) -> Optional[str]:
        """
        使用 Windmill 生成文本分析
        
        Args:
            prompt: 分析提示词
            system_instruction: 系统指令
            response_schema: 响应结构定义
            
        Returns:
            生成的分析文本，如果失败返回None
        """
        try:
            # 构建请求参数
            payload = {
                "prompt": prompt
            }
            
            if system_instruction:
                payload["system_instruction"] = system_instruction
            
            if response_schema:
                payload["responseSchema"] = response_schema
            
            # 执行作业
            result = await self.execute_job(
                folder=settings.windmill_folder,
                script=settings.windmill_script,
                payload=payload,
                max_wait_time=120  # 文本生成通常需要更长时间
            )
            
            if not result:
                return None
            
            # 提取分析结果
            if 'result' in result:
                analysis_result = result['result']

                # 处理不同的结果格式
                if isinstance(analysis_result, dict):
                    # 检查是否有 analysis 字段
                    if 'analysis' in analysis_result:
                        return analysis_result['analysis']
                    # 检查是否有 parsed 字段（可能包含结构化数据）
                    elif 'parsed' in analysis_result and analysis_result['parsed']:
                        parsed_data = analysis_result['parsed']
                        if isinstance(parsed_data, dict) and 'analysis' in parsed_data:
                            return parsed_data['analysis']
                    # 检查是否有 candidates 字段（Gemini API 格式）
                    elif 'candidates' in analysis_result:
                        candidates = analysis_result['candidates']
                        if candidates and len(candidates) > 0:
                            candidate = candidates[0]
                            if 'content' in candidate and 'parts' in candidate['content']:
                                parts = candidate['content']['parts']
                                if parts and len(parts) > 0 and 'text' in parts[0]:
                                    return parts[0]['text']
                elif isinstance(analysis_result, str):
                    return analysis_result

            logger.warning(f"无法从结果中提取分析文本，结果格式: {type(result.get('result', 'N/A'))}")
            logger.debug(f"完整结果: {result}")
            return None
            
        except Exception as e:
            logger.error(f"生成文本分析失败: {format_error_message(e, '生成文本分析')}")
            return None


# 全局客户端实例
windmill_client = WindmillClient()
